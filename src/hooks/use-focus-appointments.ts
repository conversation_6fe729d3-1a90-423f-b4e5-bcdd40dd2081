'use client';

import { useState, useEffect } from 'react';

export interface FocusAppointment {
  id: string;
  type: 'telehealth' | 'in-person';
  provider: string;
  specialty: string;
  date: string;
  time: string;
  createdAt: string;
}

const FOCUS_STORAGE_KEY = 'care-canvas-focus-appointments';

export function useFocusAppointments() {
  const [appointments, setAppointments] = useState<FocusAppointment[]>([]);

  // Load appointments from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(FOCUS_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setAppointments(parsed);
      }
    } catch (error) {
      console.error('Failed to load focus appointments:', error);
    }
  }, []);

  // Save appointments to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(FOCUS_STORAGE_KEY, JSON.stringify(appointments));
    } catch (error) {
      console.error('Failed to save focus appointments:', error);
    }
  }, [appointments]);

  const addAppointment = (appointment: Omit<FocusAppointment, 'id' | 'createdAt'>) => {
    const newAppointment: FocusAppointment = {
      ...appointment,
      id: `appointment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString()
    };
    
    setAppointments(prev => [newAppointment, ...prev]);
    return newAppointment.id;
  };

  const removeAppointment = (id: string) => {
    setAppointments(prev => prev.filter(apt => apt.id !== id));
  };

  const clearAppointments = () => {
    setAppointments([]);
  };

  return {
    appointments,
    addAppointment,
    removeAppointment,
    clearAppointments
  };
}
