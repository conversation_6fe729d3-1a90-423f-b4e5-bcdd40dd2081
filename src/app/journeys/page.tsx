'use client';

import { useState } from 'react';
import { ChatView } from '@/components/chat/ChatView';
import { CareCanvas } from '@/components/care-canvas/CareCanvas';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Heart, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useChat } from '@/contexts/ChatContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';

export default function ChatPage() {
  const [isCareCanvasOpen, setIsCareCanvasOpen] = useState(false);
  const { currentThread } = useChat();
  const isMobile = useIsMobile();
  const { open } = useSidebar();

  // Calculate header left position (same logic as CustomSidebarTrigger)
  const sidebarWidth = 'var(--sidebar-width, 280px)';
  const headerLeft = !isMobile && open ? `calc(${sidebarWidth} + 16px)` : '46px'; // 16px for padding, 26px to align with trigger

  return (
    <div className="flex-grow h-[calc(100vh)] w-full bg-genui flex overflow-hidden bg-background text-foreground" role="application" aria-label="Chat Application">
      {/* Thread Header */}
      {currentThread && (
        <header
          className="fixed top-4 z-10 border-0"
          style={{ left: headerLeft }}
        >
          <h2 className="truncate text-lg font-medium" id="chat-title">
            {currentThread.name}
          </h2>
        </header>
      )}

      {/* Main Chat Container */}
      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 ease-in-out px-4 md:px-8 lg:px-16",
        isCareCanvasOpen ? "mr-[440px] xl:px-8" : "mr-0 xl:px-32",
        currentThread ? "pt-16" : "pt-0" // Add top padding when thread header is present
      )}>
        <ChatView />
      </div>

      {/* Care Canvas Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsCareCanvasOpen(!isCareCanvasOpen)}
        className={cn(
          "fixed top-4 z-20 shadow-lg bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out",
          isCareCanvasOpen
            ? "right-[456px] opacity-0 pointer-events-none"
            : "right-4 opacity-100 pointer-events-auto"
        )}
      >
        <Heart className="h-4 w-4 mr-2" />
        Care Canvas
      </Button>

      {/* Care Canvas Side Panel */}
      <div className={cn(
        "fixed top-4 right-4 bottom-5 h-[calc(100vh-2em)] bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 shadow-xl transition-transform duration-300 ease-in-out z-10 rounded-xl overflow-y-hidden",
        "w-[440px]",
        isCareCanvasOpen ? "translate-x-0" : "translate-x-full  right-0"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Care Canvas
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Manage Your Care With Ease
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCareCanvasOpen(false)}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Scrollable Content */}
        <ScrollArea className="h-[calc(100vh-80px)]">
          <div className="p-4">
            <CareCanvas showHeader={false} />
          </div>
        </ScrollArea>
      </div>

      {/* Backdrop for mobile */}
      {isCareCanvasOpen && (
        <div
          className="fixed inset-0 bg-black/20 z-5 md:hidden"
          onClick={() => setIsCareCanvasOpen(false)}
        />
      )}
    </div>
  );
}